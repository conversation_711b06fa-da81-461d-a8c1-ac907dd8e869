# WI Optimizer (世界书 & 正则便捷管理)

这是一个用于 [SillyTavern](https://github.com/SillyTavern/SillyTavern) 的脚本，旨在提供一个集中、高效的界面来管理全局和角色的世界书（Lorebooks）与正则表达式。

脚本基于扩展[GitHub - N0VI028/JS-Slash-Runner](https://github.com/N0VI028/JS-Slash-Runner),需要提前安装.

该脚本通过在添加一个独立的管理面板，提供了便捷的使用情况查看/批量删除等功能，尤其适合拥有大量世界书和角色的用户。

本脚本改动自[DISCO](https://discord.com/channels/1291925535324110879/1389953145035296858/1393474010511048764)网友的制作，

## 整体功能

WI Optimizer 提供了一个功能丰富的管理面板，您可以进行以下操作：

- **统一视图**：在一个面板中查看和管理全局世界书、角色专属世界书、全局正则表达式和角色正则表达式。
- **快速切换**：通过顶部的标签页在不同类型的项目之间轻松切换。
- **实时搜索与过滤**：
    - 全局搜索框可以快速查找世界书或正则表达式。
    - 针对世界书，可以按“书名”、“条目名”和“关键词”进行组合过滤，精准定位。
- **完整的编辑功能**：
    - 直接在面板中创建、重命名、删除世界书和其中的条目。
    - 点击条目即可展开详细的编辑器，所有世界书条目选项（如插入位置、权重、激活逻辑等）均可可视化编辑。
    - 正则表达式的查找内容、替换内容、作用范围等均可直接修改。
- **启用/禁用切换**：一键切换世界书、条目或正则表达式的启用状态，修改即时生效。
- **多选模式**：进入多选模式后，可以批量选择项目，并进行批量启用或禁用的操作。

---

## ✨ 新增亮点功能

此版本在原有基础上进行了大量功能增强和优化，核心是提升批量管理效率和使用便捷性。

### 1. 📖 世界书使用者追踪 `(新增)`

![image-20250716144648238](assets/image-20250716144648238.png)

- **功能描述**：在“全局世界书”标签页中，每本世界书下方会清晰地列出当前正在使用它的所有角色名称。
- **解决痛点**：当您想要清理或修改一本世界书时，常常不确定它是否还被某些角色依赖。此功能让世界书的引用关系一目了然，您可以放心地进行删除或修改，避免误操作。

### 2. 🚀 强大的批量操作 `(新增)`

为了应对大量的世界书条目，新增了多项一键批量处理功能：

- **批量删除世界书**：
  - 在多选模式下，现在可以勾选多个世界书，然后点击“批量删除”按钮一次性移除它们，极大提高了清理效率。
- **一键开启防递归**：
  - 在每个世界书的条目列表顶部，新增了 **`[全开防递]`** 按钮。点击后，会自动为该世界书下的 **所有条目** 开启“防止递归”选项。
  - **解决痛点**：避免手动逐个点开条目设置，对于防止意外的递归循环非常有用。
- **一键修复关键词**：
  - 新增 **`[修复关键词]`** 按钮。点击后，会自动扫描该世界书下所有条目的关键词，并执行以下操作：
    - 将所有中文逗号 `，` 统一替换为英文逗号 `,`。
  - **解决痛点**：确保关键词格式的统一与正确，避免因格式问题导致世界书无法被正确触发。

### 4. 🎨 UI/UX 优化 `(新增)`

- **界面微调**：对面板的布局和样式进行了优化，使信息展示更加清晰，操作更加流畅。
- **数据加载**：改进了后端数据的加载和刷新逻辑，使其更加稳定和高效。

## 安装与使用

1.  确保您的酒馆安装了 [酒馆助手](https://github.com/N0VI028/JS-Slash-Runner)
2.  在扩展界面中，在“酒馆助手”的页面下添加脚本
    ![image-20250716144907304](assets/image-20250716144907304.png)
3.  起个名字，并将JS文件的内容复制进去后保存
    ![image-20250716145018978](assets/image-20250716145018978.png)
4.  刷新浏览器
5.  在魔术棒中点开使用
    ![image-20250716145103026](assets/image-20250716145103026.png)



## 许可证

AGPL-3.0